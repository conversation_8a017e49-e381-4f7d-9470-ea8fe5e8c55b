APP_VERSION='v1.0.3-alpha'
APP_PORT=5000

# This was inserted by `prisma init`:
# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# "postgresql://{PG_USER}:{PG_PASS}@{PG_HOST}:{PG_PORT}/{PG_DB_NAME}?schema=public"
DATABASE_URL="postgresql://postgres:123321@localhost:5432/db_test?schema=public"

# For QR-CODE Validator ID Key
HANZ_CRYPTO_KEY="jiovej89envioev893ho3v983ncui439f439fhfwe"

# mail sender secret env key
EMAIL_USER="<EMAIL>"
EMAIL_PASS="okei wedc awsq mskd"